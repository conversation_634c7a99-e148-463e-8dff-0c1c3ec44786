<template>
    <form ref="contactForm" aria-labelledby="contact-info-heading" class="tw-pb-6 tw-space-y-8">
        <div class="tw-grid tw-grid-cols-6 tw-gap-6">
            <div class="tw-col-span-6 sm:tw-col-span-3">
                <label class="tw-block tw-text-sm tw-font-medium tw-text-gray-700" for="first_name">First name</label>
                <input id="first_name" v-model="cart.customer.first_name" autocomplete="given-name" class="tw-mt-1 focus:tw-ring-theme-action-color focus:tw-border-theme-action-color tw-block tw-w-full tw-shadow-sm sm:tw-text-sm tw-border-gray-300 tw-rounded-md" name="first_name" required type="text"/>
            </div>

            <div class="tw-col-span-6 sm:tw-col-span-3">
                <label class="tw-block tw-text-sm font-medium text-gray-700" for="last-name">Last name</label>
                <input id="last-name" v-model="cart.customer.last_name" autocomplete="family-name" class="tw-mt-1 focus:tw-ring-theme-action-color focus:tw-border-theme-action-color tw-block tw-w-full tw-shadow-sm sm:tw-text-sm tw-border-gray-300 tw-rounded-md" name="last-name" required type="text"/>
            </div>

            <div class="tw-col-span-6 sm:tw-col-span-3">
                <div>
                    <div class="tw-flex tw-items-center tw-space-x-1">
                        <label class="tw-block tw-text-sm font-medium text-gray-700" for="phone-number">Mobile phone</label>
                        <Popover class="tw-relative">
                            <PopoverButton class=" tw-flex tw-items-center tw-justify-center">
                                <ExclamationCircleIcon class="tw-text-gray-500 tw-h-4 tw-w-4"/>
                            </PopoverButton>

                            <PopoverPanel class="tw-absolute tw-z-10 tw-bg-white tw-rounded-lg tw-shadow-lg tw-px-3 tw-py-2 tw-text-sm tw-border tw-border-gray-200">
                                <p>Notifications about your order may be communicated via text messages.</p>
                            </PopoverPanel>
                        </Popover>

                    </div>
                    <div class="tw-relative tw-mt-1 tw-rounded-md tw-shadow-sm">
                        <input id="phone-number"
                               v-model="cart.customer.phone"
                               :aria-invalid="hasPhoneErrors"
                               :class="[
                                   hasPhoneErrors ? 'tw-pr-10 tw-text-red-900 tw-border-red-300 placeholder:tw-text-red-300 focus:tw-ring-red-500 tw-ring-1 tw-ring-inset focus:tw-ring-2 focus:tw-ring-inset ' : 'tw-border-gray-300 focus:tw-ring-theme-action-color focus:tw-border-theme-action-color',
                                   'tw-block tw-w-full tw-rounded-md tw-border tw-py-1.5 sm:tw-text-sm sm:tw-leading-6'
                               ]"
                               aria-describedby="phone-error"
                               autocomplete="phone-number"
                               maxlength="15"
                               minlength="5"
                               name="phone-number"
                               placeholder=""
                               required
                               type="tel"
                        >
                        <div v-show="hasPhoneErrors" class="tw-pointer-events-none tw-absolute tw-inset-y-0 tw-right-0 tw-flex tw-items-center tw-pr-3">
                            <svg aria-hidden="true" class="tw-h-5 tw-w-5 tw-text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                <path clip-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" fill-rule="evenodd"></path>
                            </svg>
                        </div>
                    </div>
                    <p v-if="hasPhoneErrors && ! phoneNumberLookup?.valid" id="phone-error" class="tw-mt-2 tw-text-sm tw-text-red-600">
                        We could not verify this number. Please check the format (e.g., ******-123-4567) or continue if the number is correct.
                    </p>
                    <p v-if="hasPhoneErrors && phoneNumberLookup?.valid && phoneNumberLookup?.type !== 'mobile'" id="phone-error" class="tw-mt-2 tw-text-sm tw-text-red-600">
                        This number may not be able to receive text updates. Please enter a number that can receive text updates about this order.
                    </p>
                </div>
            </div>

            <div class="tw-col-span-6 sm:tw-col-span-3">
                <label class="tw-block tw-text-sm tw-font-medium tw-text-gray-700" for="email-address">Email address</label>
                <input id="email-address" :value="cart.customer.email" class="tw-mt-1 tw-bg-gray-100 tw-cursor-not-allowed tw-block tw-user-select-none focus:tw-ring-transparent focus:tw-border-theme-action-color focus:tw-border-gray-300 tw-w-full tw-shadow-sm sm:tw-text-sm tw-border-gray-300 tw-rounded-md" name="email-address" readonly required type="email"/>
            </div>

            <div v-if=" ! user.subscribed_to_sms_marketing_at && checkout.sms_opt_in_settings.message && checkout.sms_opt_in_settings.sms_legal_consent_message" class="tw-col-span-6">

                <div class="tw-bg-theme-brand-color/10 tw-px-4 tw-py-6 tw-rounded-md">
                    <div class="tw-flex">
                        <input id="sms-opt-in" :checked="cart.customer.opt_in_to_sms" class="tw-mt-1.5 tw-h-4 tw-w-4 tw-border-gray-300 tw-rounded tw-text-theme-action-color focus:tw-ring-theme-action-color" name="sms-opt-in" type="checkbox" @input="cart.toggleCustomerSmsOptIn">
                        <div class="tw-ml-3">
                            <label class="tw-text-base tw-font-semibold tw-leading-0 tw-text-gray-800" for="sms-opt-in" v-html="checkout.sms_opt_in_settings.message"></label>
                            <p class="tw-m-0 tw-mt-3 tw-text-gray-700 tw-text-xs" v-html="checkout.sms_opt_in_settings.sms_legal_consent_message"></p>
                        </div>
                    </div>

                </div>

            </div>
            <div v-if="user.order_count > 0 && ! user.is_subscriber" class="tw-col-span-6 sm:tw-col-span-3">
                <div class="tw-flex tw-items-center">
                    <input id="customer-save-for-future" :checked="cart.customer.save_for_later" class="tw-h-4 tw-w-4 tw-border-gray-300 tw-rounded tw-text-theme-action-color focus:tw-ring-theme-action-color" name="customer-save-for-future" type="checkbox" @input="cart.toggleCustomerSaveForLater">
                    <div class="tw-ml-2">
                        <label class="tw-text-sm tw-font-medium tw-text-gray-900" for="customer-save-for-future">Save for future purchases</label>
                    </div>
                </div>
            </div>

            <div v-show="allowsGifting && !cart.subscription" class="tw-border-t tw-border-gray-200 tw-pt-6 tw-col-span-6 ">
                <div class="tw-flex">
                    <input id="gift-opt-in" :checked="cart.is_gift" class="tw-mt-1 tw-h-4 tw-w-4 tw-border-gray-300 tw-rounded tw-text-theme-action-color focus:tw-ring-theme-action-color" name="gift-opt-in" type="checkbox" @input="cart.toggleGiftOptIn">
                    <div class="tw-ml-3">
                        <label class="tw-text-sm tw-leading-0 tw-cursor-pointer tw-text-gray-700" for="gift-opt-in">This order is a gift</label>
                    </div>
                </div>
            </div>
            <div v-show="cart.is_gift" class="tw-col-span-6 sm:tw-col-span-4">
                <label class="tw-block tw-text-sm tw-font-medium tw-text-gray-700" for="recipient-email-address">Recipient email</label>
                <input id="email-address" v-model="cart.recipient_email" class="tw-mt-1 tw-bg-white tw-block focus:tw-ring-theme-action-color focus:tw-border-theme-action-color focus:tw-border-gray-300 tw-w-full tw-shadow-sm sm:tw-text-sm tw-border-gray-300 tw-rounded-md" name="recipient-email-address" type="email"/>
            </div>
            <div v-show="cart.is_gift" class="tw-col-span-6">
                <div>
                    <label class="tw-block tw-text-sm tw-font-medium tw-text-gray-700" for="recipient-notes">Gift message</label>
                    <textarea id="recipient-notes" v-model="cart.recipient_notes" class="tw-mt-1  focus:tw-ring-theme-action-color focus:tw-border-theme-action-color tw-block tw-w-full tw-shadow-sm  placeholder:tw-text-gray-400 placeholder:tw-italic sm:tw-text-sm tw-border-gray-300 tw-rounded-md" maxlength="500" name="recipient-notes" rows="3"/>
                    <p id="recipient-notes-description" class="tw-mt-2 tw-text-sm tw-text-gray-500">
                        <span v-text="recipientNoteCharactersRemaining"></span> characters remaining</p>
                </div>
            </div>
        </div>
        <button
            :class="[
                cart.contactInformationIsComplete ? 'tw-bg-theme-action-color tw-text-white hover:tw-bg-theme-action-color/70 focus:tw-ring-theme-action-color ' : 'tw-bg-gray-100 tw-text-gray-500 tw-cursor-not-allowed',
                'tw-mt-6 tw-flex tw-items-center tw-justify-center tw-w-full tw-cursor-pointer tw-border tw-border-transparent tw-rounded-md tw-shadow-sm tw-py-2 tw-px-4 tw-text-sm tw-font-semibold focus:tw-outline-none focus:tw-ring-2 focus:tw-ring-offset-2'
            ]"
            :disabled=" ! cart.contactInformationIsComplete || saving"
            type="button"
            @click="save(close)"
        >
            <svg v-if="saving" class="tw-animate-spin tw--ml-1 tw-mr-3 tw-h-5 tw-w-5 tw-text-gray-200" fill="none" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <circle class="tw-opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="tw-opacity-75" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" fill="currentColor"></path>
            </svg>
            <span v-text="saving ? 'Saving...' : 'Continue'"></span>
        </button>
    </form>
</template>

<script>
import { Disclosure, DisclosureButton, Popover, PopoverButton, PopoverPanel } from '@headlessui/vue';
import { CheckCircleIcon, ExclamationCircleIcon } from '@heroicons/vue/outline';
import { useUserStore } from '../../stores/user';
import { useCartStore } from '../../stores/cart';
import { useTenantStore } from '../../stores/tenant';
import { useCheckoutStore } from '../../stores/checkout';
import { computed, ref, watch } from 'vue';
import axios from 'axios';

export default {
    name: 'ContactInformationForm',

    emits: ['completed'],

    props: {
        allowsGifting: { default: true }
    },

    components: {
        Disclosure,
        DisclosureButton,
        Popover, PopoverButton, PopoverPanel,
        CheckCircleIcon,
        ExclamationCircleIcon
    },

    setup(props, { emit }) {
        const user = useUserStore();
        const cart = useCartStore();
        const tenant = useTenantStore();
        const checkout = useCheckoutStore();

        const saving = ref(false);
        const contactForm = ref(null);
        const phoneNumberLookup = ref(null);

        watch(
            () => cart.customer.phone,
            () => phoneNumberLookup.value = null
        );

        const hasPhoneErrors = computed(() => {
            return cart.customer.phone
                && phoneNumberLookup.value !== null
                && (
                    !phoneNumberLookup.value.valid
                    || (phoneNumberLookup.value.valid && phoneNumberLookup.value.type !== 'mobile')
                );
        });

        const lookupPhoneNumber = () => {
            if (!cart.customer.phone) return;

            axios.post('/api/theme/phone-number-lookups', {
                phone_number: cart.customer.phone
            }).then(({ data }) => {
                saving.value = false;
                phoneNumberLookup.value = data;

                if (data.valid && data.type === 'mobile') {
                    emit('completed');
                }
            }).catch(error => {
                saving.value = false;
                // In development mode, assume phone number is valid if API fails
                if (window.location.hostname.includes('test') || window.location.hostname.includes('localhost')) {
                    phoneNumberLookup.value = { valid: true, type: 'mobile' };
                    emit('completed');
                } else {
                    phoneNumberLookup.value = { valid: false, type: null };
                }
            });
        };

        const save = () => {
            if (saving.value) return;

            if (!contactForm.value.checkValidity()) {
                if (contactForm.value.reportValidity) {
                    contactForm.value.reportValidity();
                }

                return;
            }

            if (!cart.contactInformationIsComplete) return;

            if (phoneNumberLookup.value !== null) {
                phoneNumberLookup.value = null;
                emit('completed');
                return;
            }

            saving.value = true;
            lookupPhoneNumber();
        };

        const recipientNoteCharactersRemaining = computed(() => Math.max(500 - cart.recipient_notes.length, 0));

        return {
            contactForm,
            user,
            cart,
            tenant,
            checkout,
            save,
            saving,
            phoneNumberLookup,
            hasPhoneErrors,
            recipientNoteCharactersRemaining
        };
    }
};
</script>
