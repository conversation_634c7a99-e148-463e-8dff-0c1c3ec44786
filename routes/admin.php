<?php

use App\Http\Controllers\Admin\Account\AccountApiTokenController;
use App\Http\Controllers\Admin\Account\AccountController;
use App\Http\Controllers\Admin\Account\AccountNotificationController;
use App\Http\Controllers\Admin\Account\AccountSecurityController;
use App\Http\Controllers\Admin\Account\ApiKeysController;
use App\Http\Controllers\Admin\API\SluggableCreateSlugController;
use App\Http\Controllers\Admin\AppController;
use App\Http\Controllers\Admin\CategoryController;
use App\Http\Controllers\Admin\Collection\CollectionController;
use App\Http\Controllers\Admin\Collection\CollectionPhotoController;
use App\Http\Controllers\Admin\Collection\CollectionProductController;
use App\Http\Controllers\Admin\Collection\SubcollectionController;
use App\Http\Controllers\Admin\Collection\SubcollectionPhotoController;
use App\Http\Controllers\Admin\CouponController;
use App\Http\Controllers\Admin\CustomerSettingController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\DeliveryController;
use App\Http\Controllers\Admin\DuplicatePageController;
use App\Http\Controllers\Admin\FilterController;
use App\Http\Controllers\Admin\FooterWidgetController;
use App\Http\Controllers\Admin\GiftCardCodeController;
use App\Http\Controllers\Admin\GiftCardController;
use App\Http\Controllers\Admin\Integration\DripIntegrationController;
use App\Http\Controllers\Admin\Integration\SubscribeSaveIntegrationController;
use App\Http\Controllers\Admin\LocationController;
use App\Http\Controllers\Admin\MenuController;
use App\Http\Controllers\Admin\MenuItemController;
use App\Http\Controllers\Admin\MessageController;
use App\Http\Controllers\Admin\NotifyController;
use App\Http\Controllers\Admin\Order\BulkCancelOrderController;
use App\Http\Controllers\Admin\Order\OrderBulkExportController;
use App\Http\Controllers\Admin\Order\OrderBulkMessageController;
use App\Http\Controllers\Admin\Order\OrderBulkPrintController;
use App\Http\Controllers\Admin\Order\OrderBulkProcessController;
use App\Http\Controllers\Admin\Order\OrderBulkUpdateFieldController;
use App\Http\Controllers\Admin\Order\OrderBulkUpdateStatusController;
use App\Http\Controllers\Admin\Order\OrderBulkUpdateTagController;
use App\Http\Controllers\Admin\Order\OrderCanceledController;
use App\Http\Controllers\Admin\Order\OrderCardPaymentController;
use App\Http\Controllers\Admin\Order\OrderConfirmedController;
use App\Http\Controllers\Admin\Order\OrderConfirmedNotificationController;
use App\Http\Controllers\Admin\Order\OrderController;
use App\Http\Controllers\Admin\Order\OrderCustomNotificationController;
use App\Http\Controllers\Admin\Order\OrderDiscountController;
use App\Http\Controllers\Admin\Order\OrderFeeController;
use App\Http\Controllers\Admin\Order\OrderItemController;
use App\Http\Controllers\Admin\Order\OrderItemWeightController;
use App\Http\Controllers\Admin\Order\OrderPackedNotificationController;
use App\Http\Controllers\Admin\Order\OrderPaidController;
use App\Http\Controllers\Admin\Order\OrderPaymentController;
use App\Http\Controllers\Admin\Order\OrderPrintedController;
use App\Http\Controllers\Admin\Order\OrderRefundController;
use App\Http\Controllers\Admin\Order\OrderTagController;
use App\Http\Controllers\Admin\PackingGroupController;
use App\Http\Controllers\Admin\PageController;
use App\Http\Controllers\Admin\PageWidgetController;
use App\Http\Controllers\Admin\PageWidgetPhotoController;
use App\Http\Controllers\Admin\PageWidgetSortController;
use App\Http\Controllers\Admin\Password\PasswordForgotController;
use App\Http\Controllers\Admin\Password\PasswordResetController;
use App\Http\Controllers\Admin\PaymentController;
use App\Http\Controllers\Admin\PaymentOptionController;
use App\Http\Controllers\Admin\PhotoController;
use App\Http\Controllers\Admin\PhotoTagController;
use App\Http\Controllers\Admin\Pickup\PickupController;
use App\Http\Controllers\Admin\Pickup\PickupFeeController;
use App\Http\Controllers\Admin\Post\PostController;
use App\Http\Controllers\Admin\Post\PostPhotoController;
use App\Http\Controllers\Admin\Post\PostTagController;
use App\Http\Controllers\Admin\ProcessOrderController;
use App\Http\Controllers\Admin\Product\ProductBundleController;
use App\Http\Controllers\Admin\Product\ProductCollectionController;
use App\Http\Controllers\Admin\Product\ProductController;
use App\Http\Controllers\Admin\Product\ProductDescriptionController;
use App\Http\Controllers\Admin\Product\ProductDuplicateController;
use App\Http\Controllers\Admin\Product\ProductImportController;
use App\Http\Controllers\Admin\Product\ProductPhotoController;
use App\Http\Controllers\Admin\Product\ProductPrintedController;
use App\Http\Controllers\Admin\Product\ProductSeoController;
use App\Http\Controllers\Admin\Product\ProductTagController;
use App\Http\Controllers\Admin\Product\ProductVariantsController as ProductProductVariantsController;
use App\Http\Controllers\Admin\Product\SyncSubscriptionReserveInventory;
use App\Http\Controllers\Admin\ProposalController;
use App\Http\Controllers\Admin\ProtocolController;
use App\Http\Controllers\Admin\Recipe\RecipeController;
use App\Http\Controllers\Admin\Recipe\RecipeIngredientController;
use App\Http\Controllers\Admin\Recipe\RecipePhotoController;
use App\Http\Controllers\Admin\Recipe\RecipeTagController;
use App\Http\Controllers\Admin\Reports\AccountCreditAppliedReportController;
use App\Http\Controllers\Admin\Reports\DeliverySalesReportController;
use App\Http\Controllers\Admin\Reports\FirstTimeRenewalReportController;
use App\Http\Controllers\Admin\Reports\HarvestReportController;
use App\Http\Controllers\Admin\Reports\IncomeAnalysisReportController;
use App\Http\Controllers\Admin\Reports\InventoryReportController;
use App\Http\Controllers\Admin\Reports\ProductSalesReportController;
use App\Http\Controllers\Admin\Reports\StockOutReportController;
use App\Http\Controllers\Admin\Reports\SubscriptionInventoryReportController;
use App\Http\Controllers\Admin\Reports\UnusedBalancesReportController;
use App\Http\Controllers\Admin\RestockLogReportController;
use App\Http\Controllers\Admin\Schedule\ScheduleController;
use App\Http\Controllers\Admin\Schedule\ScheduleDateController;
use App\Http\Controllers\Admin\Schedule\ScheduleReminderController;
use App\Http\Controllers\Admin\SendGiftCardCodeController;
use App\Http\Controllers\Admin\SessionController;
use App\Http\Controllers\Admin\SettingController;
use App\Http\Controllers\Admin\Settings\Legal\CheckoutDocumentController;
use App\Http\Controllers\Admin\Settings\Legal\LegalDocumentController;
use App\Http\Controllers\Admin\Settings\Legal\PrivacyDocumentController;
use App\Http\Controllers\Admin\Settings\Legal\TermsOfServiceDocumentController;
use App\Http\Controllers\Admin\Settings\ThemeSettingsController;
use App\Http\Controllers\Admin\SubscriptionController;
use App\Http\Controllers\Admin\TagController;
use App\Http\Controllers\Admin\TeamController;
use App\Http\Controllers\Admin\Template\TemplateController;
use App\Http\Controllers\Admin\Template\TemplatePreviewController;
use App\Http\Controllers\Admin\Template\TemplateSegmentController;
use App\Http\Controllers\Admin\ThemeController;
use App\Http\Controllers\Admin\User\UserCardController;
use App\Http\Controllers\Admin\User\UserController;
use App\Http\Controllers\Admin\User\UserPhotoController;
use App\Http\Controllers\Admin\User\UserTagController;
use App\Http\Controllers\Admin\Vendor\VendorController;
use App\Http\Controllers\Admin\Vendor\VendorPhotoController;
use App\Http\Middleware\Theme\IdentifyGuestShopper;
use App\Models\Setting;
use Illuminate\Support\Facades\Route;

Route::get('play', function () {
    Setting::updateOrCreate(['key' => 'stripe_public_key'], ['value' => encrypt('standard_123')]);
});

Route::get('/admin/login', [SessionController::class, 'index'])->name('admin.login');
Route::post('/admin/login', [SessionController::class, 'store'])->name('admin.authenticate');
Route::get('/admin/logout', [SessionController::class, 'destroy'])->name('admin.logout');

Route::get('/admin/password/forgot', [PasswordForgotController::class, 'show']);
Route::post('/admin/password/forgot', [PasswordForgotController::class, 'store']);

Route::get('/admin/password/reset/{token}', [PasswordResetController::class, 'show']);
Route::post('/admin/password/reset', [PasswordResetController::class, 'store']);

Route::prefix('/admin')
    ->middleware(['auth.editor'])
    ->group(function () {
    // Dashboard
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');

    // Setup Checklist
    Route::get('/welcome', function() {
        return redirect('/admin', 301);
    });

    // Photos
    Route::get('/media', [PhotoController::class, 'index']);
    Route::get('/photos', [PhotoController::class, 'index']);

    // Photo Tags
    Route::resource('photos.tags', PhotoTagController::class)->except('create', 'show');

    // Posts
    Route::resource('posts', PostController::class, ['as' => 'admin'])->except('create');

    // Post Photos
    Route::put('/posts/{posts}/photos', [PostPhotoController::class, 'update']);

    // Post Tags
    Route::get('/posts/{posts}/tags', [PostTagController::class, 'index']);
    Route::post('/posts/{posts}/tags', [PostTagController::class, 'store']);
    Route::put('/posts/{posts}/tags/{tags}', [PostTagController::class, 'update']);
    Route::delete('/posts/{posts}/tags/{tags}', [PostTagController::class, 'destroy']);

    // Recipe Tags
    Route::get('/recipes/{recipe}/tags', [RecipeTagController::class, 'index']);
    Route::post('/recipes/{recipe}/tags', [RecipeTagController::class, 'store'])->name('admin.recipes.tags.store');
    Route::put('/recipes/{recipe}/tags/{tag}', [RecipeTagController::class, 'update']);
    Route::delete('/recipes/{recipe}/tags/{tag}', [RecipeTagController::class, 'destroy']);

    // Recipe Ingredients
    Route::get('/recipes/{recipes}/ingredients', [RecipeIngredientController::class, 'index']);
    Route::post('/recipes/{recipes}/ingredients', [RecipeIngredientController::class, 'store']);
    Route::put('/recipes/{recipes}/ingredients/{ingredients}', [RecipeIngredientController::class, 'update']);
    Route::delete('/recipes/{recipes}/ingredients/{ingredients}', [RecipeIngredientController::class, 'destroy']);

    // Recipe Photo
    Route::put('/recipes/{recipes}/photo', [RecipePhotoController::class, 'update']);

    // Recipes
    Route::get('recipes/{slug}', function($slug) { return to_route('recipes.show', [$slug]); });
    Route::resource('recipes', RecipeController::class, ['as' => 'admin']);

    // Tags
    Route::resource('tags', TagController::class, ['as' => 'admin']);
});

Route::prefix('/admin')->middleware('auth.admin')->group(function () {
    // API Keys
    Route::resource('api-keys', ApiKeysController::class);

    // Account
    Route::get('/account', [AccountController::class, 'edit']);
    Route::get('/account/profile', [AccountController::class, 'edit']);
    Route::put('/account', [AccountController::class, 'update']);

    // Account Notifications
    Route::get('/account/notifications', [AccountNotificationController::class, 'edit']);
    Route::put('/account/notifications', [AccountNotificationController::class, 'update']);

    // Account Security
    Route::get('/account/security', [AccountSecurityController::class, 'edit']);
    Route::put('/account/security', [AccountSecurityController::class, 'update']);

    // Account API Security
    Route::get('/account/api-tokens', [AccountApiTokenController::class, 'show'])->name('admin.account.api-tokens.show');
    Route::post('/account/api-tokens', [AccountApiTokenController::class, 'store'])->name('admin.account.api-tokens.store');
    Route::delete('/account/api-tokens/{token}', [AccountApiTokenController::class, 'destroy'])->name('admin.account.api-tokens.destroy');
});

Route::prefix('/admin')->middleware(['auth.admin'])->group(function () {
    // Collection Photo
    Route::put('/collections/{collections}/photo', [CollectionPhotoController::class, 'update']);

    // Subcollection Photo
    Route::put('/subcollections/{subcollections}/photo', [SubcollectionPhotoController::class, 'update']);

    // Collection Product
    Route::get('/collections/{collection}/products', [CollectionProductController::class, 'index']);
    Route::post('/collections/{collection}/products', [CollectionProductController::class, 'store']);
    Route::put('/collections/{collection}/products', [CollectionProductController::class, 'update']);
    Route::delete('/collections/{collection}/products/{product}', [CollectionProductController::class, 'destroy']);

    // Collections
    Route::resource('collections', CollectionController::class, ['as' => 'admin']);

    // Subcollections
    Route::resource('subcollections', SubcollectionController::class, ['as' => 'admin']);

    // Coupons
    Route::resource('coupons', CouponController::class)->only('index');

    // Drip
    Route::put('/settings/integrations/drip', [DripIntegrationController::class, 'update'])->name('admin.integrations.drip.update');

    // Subscribe & Save
    Route::put('/settings/integrations/subscribe-save', [SubscribeSaveIntegrationController::class, 'update'])->name('admin.integrations.subscribe-save.update');

    // Filters
    Route::get('/filters', [FilterController::class, 'index']);
    Route::get('/filters/{filters}/edit', [FilterController::class, 'edit']);
    Route::post('/filters', [FilterController::class, 'store']);
    Route::put('/filters/{filters}', [FilterController::class, 'update']);
    Route::delete('/filters/{filters}', [FilterController::class, 'destroy']);

    // Footer Widgets
    Route::get('/footer/widgets', [FooterWidgetController::class, 'index']);
    Route::post('/footer/widgets', [FooterWidgetController::class, 'store'])
        ->middleware([IdentifyGuestShopper::class]) // ViewServiceProvider shares cart with theme::widgets.* files
        ->name('admin.footer.widgets.store');

    Route::get('/footer/widgets/edit', [FooterWidgetController::class, 'edit']);
    Route::put('/footer/widgets/{widget}', [FooterWidgetController::class, 'update'])
        ->middleware([IdentifyGuestShopper::class]) // ViewServiceProvider shares cart with theme::widgets.* files
        ->name('admin.footer.widgets.update');

    Route::delete('/footer/widgets/{widget}', [FooterWidgetController::class, 'destroy'])->name('admin.footer.widgets.destroy');

    // Locations
    Route::get('/locations/{locations}', [LocationController::class, 'index'])->name('admin.locations.index');
    Route::resource('locations', LocationController::class, ['as' => 'admin']);

    // Logistics
    Route::prefix('logistics')->group(function () {
        // Delivery
        Route::resource('delivery', DeliveryController::class, ['as' => 'admin']);

        // Pickup Locations
        Route::resource('pickups', PickupController::class, ['as' => 'admin']);
    });

    // Pickup Fees
    Route::get('/pickups/{pickup}/fees', [PickupFeeController::class, 'index'])->name('admin.pickups.fees.index');
    Route::post('/pickups/{pickup}/fees', [PickupFeeController::class, 'store'])->name('admin.pickups.fees.store');
    Route::put('/pickups/{pickup}/fees/{fee}', [PickupFeeController::class, 'update'])->name('admin.pickups.fees.update');
    Route::delete('/pickups/{pickup}/fees/{fee}', [PickupFeeController::class, 'destroy'])->name('admin.pickups.fees.destroy');

    // Menus
    Route::get('/menus', [MenuController::class, 'index'])->name('admin.menus.edit');
    Route::get('/menus/{menu}/edit', [MenuController::class, 'edit']);

    Route::get('/menus/{menu}/items', [MenuItemController::class, 'index']);
    Route::put('/menus/{menu}/items/{item}', [MenuItemController::class, 'update']);
    Route::delete('/menus/{menu}/items/{item}', [MenuItemController::class, 'destroy']);

    // Menu Items
    Route::get('/menu-items', [MenuItemController::class, 'edit'])->name('admin.menu-items');
    Route::post('/menu-items', [MenuItemController::class, 'store'])->name('admin.menu-items.store');
    Route::put('/menu-items/update', [MenuItemController::class, 'update'])->name('admin.menu-items.update');
    Route::post('/menu-items/sort', [MenuItemController::class, 'sort']);

    // Messages
    Route::get('/message', [MessageController::class, 'index']);
    Route::put('/message', [MessageController::class, 'update']);

    // Notify
    Route::post('/notify', [NotifyController::class, 'store']);

    // Orders
    Route::resource('orders', OrderController::class, ['as' => 'admin']);

    // Order Bulk Update
    Route::put('/orders/bulk-update/cancel/{orders?}', BulkCancelOrderController::class)->name('admin.orders.bulk-cancel');
    Route::put('/orders/bulk-update/message', [OrderBulkMessageController::class, 'update'])->name('admin.orders.notifications.bulk');
    Route::put('/orders/bulk-update/status/{status}', [OrderBulkUpdateStatusController::class, 'update'])->name('admin.orders.bulk-update.status');
    Route::put('/orders/bulk-update/field/{field}', [OrderBulkUpdateFieldController::class, 'update']);
    Route::put('/orders/bulk-update/print/{print}', [OrderBulkPrintController::class, 'update'])->name('admin.orders.bulk-update.print');
    Route::put('/orders/bulk-update/export/{export}', [OrderBulkExportController::class, 'update'])->name('admin.orders.bulk-update.export');
    Route::put('/orders/bulk-update/tag/{tag}', [OrderBulkUpdateTagController::class, 'update'])->name('admin.orders.bulk-update.tag');
    Route::put('/orders/bulk-update/process/{type?}', [OrderBulkProcessController::class, 'update'])->name('admin.orders.bulk-process');

    // Order Notifications
    Route::post('/orders/{order}/notifications/confirmed', [OrderConfirmedNotificationController::class, 'store'])->name('admin.orders.notifications.confirmed');
    Route::post('/orders/{order}/notifications/packed', [OrderPackedNotificationController::class, 'store']);
    Route::post('/orders/{order}/notifications/custom', [OrderCustomNotificationController::class, 'store'])->name('admin.orders.notifications.custom');

    // Order Items
    Route::resource('orders.items', OrderItemController::class, ['as' => 'admin']);

    // Order Item Weight
    Route::put('/orders/{order}/items/{item}/weights', [OrderItemWeightController::class, 'update']);

    // Order Fees
    Route::resource('orders.fees', OrderFeeController::class, ['as' => 'admin'])
        ->only('index', 'store', 'update', 'destroy');

    // Order Fees
    Route::resource('orders.discounts', OrderDiscountController::class)->only('store', 'destroy');

    // Order Payment
    Route::resource('orders.payments', OrderPaymentController::class)->only('store', 'update', 'destroy');
    Route::post('/orders/{order}/card-payments', [OrderCardPaymentController::class, 'store'])->name('admin.orders.card-payments');

    // Refunds
    Route::post('/orders/{order}/card-payments/{payment}/refunds', [OrderRefundController::class, 'store'])->name('admin.payments.refunds.store');

    // Order Paid
    Route::post('/orders/{orders}/paid', [OrderPaidController::class, 'store']);
    Route::delete('/orders/{orders}/paid', [OrderPaidController::class, 'destroy']);

    // Order Processed
    Route::post('/orders/{order}/process', [ProcessOrderController::class, 'store'])->name('admin.orders.process');

    // Order Print
    Route::get('/orders/{order}/print/{print}', [OrderPrintedController::class, 'get']);

    // Order Confirmed
    Route::post('/orders/{order}/confirmed', [OrderConfirmedController::class, 'store'])->name('admin.orders.confirm.store');

    // Order Canceled
    Route::post('/orders/{order}/canceled', [OrderCanceledController::class, 'store'])->name('admin.orders.cancel.store');

    // Order Tags
    Route::get('/orders/{orders}/tags', [OrderTagController::class, 'index']);
    Route::post('/orders/{orders}/tags', [OrderTagController::class, 'store']);
    Route::put('/orders/{orders}/tags/{tags}', [OrderTagController::class, 'update']);
    Route::delete('/orders/{products}/tags/{tags}', [OrderTagController::class, 'destroy']);

    // Page Widgets
    Route::get('/pages/{page}/widgets', [PageWidgetController::class, 'index']);
    Route::post('/pages/{page}/widgets', [PageWidgetController::class, 'store']);
    Route::post('/pages/{page}/widget-sort', [PageWidgetSortController::class, 'store'])->name('admin.pages.widget-sort.store');
    Route::put('/pages/{page}/widgets/{widget}', [PageWidgetController::class, 'update']);
    Route::delete('/pages/{page}/widgets/{widget}', [PageWidgetController::class, 'destroy']);
    Route::put('/pages/{pages}/widgets/{widgets}/photo', [PageWidgetPhotoController::class, 'update']);

    Route::post('/duplicate-page/{pages}', [DuplicatePageController::class, 'store']);

    // Pages
    Route::resource('pages', PageController::class, ['as' => 'admin']);

    // Payments
    Route::get('/settings/payment-options', [PaymentOptionController::class, 'index']);

    // TODO: deprecate payment routes below
    Route::get('/settings/payments', [PaymentController::class, 'index']);
    Route::get('/settings/payments/{id}/edit', [PaymentController::class, 'edit'])->name('admin.payments.edit');
    Route::put('/settings/payments/{id}', [PaymentController::class, 'update']);

    Route::get('/settings/payments/card/{gateway}', [PaymentController::class, 'editGateway']);

    // Product Bundle
    Route::get('/products/{products}/bundles', [ProductBundleController::class, 'index']);
    Route::post('/products/{products}/bundles', [ProductBundleController::class, 'store']);
    Route::put('/products/{products}/bundles/{bundles}', [ProductBundleController::class, 'update']);
    Route::delete('/products/{products}/bundles/{bundles}', [ProductBundleController::class, 'destroy']);

    // Product Collections
    Route::get('/products/{products}/collections', [ProductCollectionController::class, 'index']);
    Route::post('/products/{products}/collections', [ProductCollectionController::class, 'store']);
    Route::delete('/products/{products}/collections/{collections}', [ProductCollectionController::class, 'destroy']);

    // Product Photo
    Route::put('/products/{products}/photo', [ProductPhotoController::class, 'update']);

    // Product Printed
    Route::get('/products/{products}/printed/{template}', ProductPrintedController::class);

    // Product Tags
    Route::get('/products/{products}/tags', [ProductTagController::class, 'index']);
    Route::post('/products/{products}/tags', [ProductTagController::class, 'store']);
    Route::put('/products/{products}/tags/{tags}', [ProductTagController::class, 'update']);
    Route::delete('/products/{products}/tags/{tags}', [ProductTagController::class, 'destroy']);

    // Product Variants
    Route::get('/products/{product}/variants', [ProductProductVariantsController::class, 'index']);
    Route::post('/products/{product}/variants', [ProductProductVariantsController::class, 'store']);
    Route::put('/products/{product}/variants/{variant?}', [ProductProductVariantsController::class, 'update']);
    Route::delete('/products/{product}/variants/{variant}', [ProductProductVariantsController::class, 'destroy']);

    // Products
    Route::get('/products/all', [ProductController::class, 'all']);
    Route::post('/products/import', [ProductImportController::class, 'store'])->name('admin.products.import.store');
    Route::post('products/sync-reserve-inventory', SyncSubscriptionReserveInventory::class)->name('admin.products.sync-reserve-inventory.store');

    Route::resource('products', ProductController::class, ['as' => 'admin']);

    // Product Description
    Route::put('/products/{product}/description', [ProductDescriptionController::class, 'update'])
        ->name('admin.products.description.update');

    // Product SEO
    Route::put('/products/{product}/seo', [ProductSeoController::class, 'update'])->name('admin.products.update.seo');

    // Duplicate Products
    Route::post('/products/{product}/duplicate', ProductDuplicateController::class)
        ->name('admin.products.duplicate');

    // Gift Cards
    Route::resource('gift-cards', GiftCardController::class)->names([
        'index' => 'admin.gift-cards.index',
        'edit' => 'admin.gift-cards.edit',
        'update' => 'admin.gift-cards.update',
    ])->except('create', 'show');

    Route::get('/gift-cards/{gift_card}/codes/{code:code}', [GiftCardCodeController::class, 'show'])
        ->name('admin.gift-cards.codes.show');

    Route::patch('/gift-cards/{gift_card}/codes/{code:code}', [GiftCardCodeController::class, 'update'])
        ->name('admin.gift-cards.codes.update');

    Route::post('/gift-card-codes/{code:code}/send', SendGiftCardCodeController::class)
        ->name('admin.gift-cards-codes.send.store');

    // Protocols
    Route::resource('protocols', ProtocolController::class, ['as' => 'admin']);

    // Proposals
    Route::resource('proposals', ProposalController::class, ['as' => 'admin']);

    // Reports
    Route::get('/reports/stock-out', [StockOutReportController::class, 'index'])->name('admin.reports.stock-out');
    Route::get('/reports/delivery-sales', [DeliverySalesReportController::class, 'index'])->name('admin.reports.delivery-sales');
    Route::get('/reports/income-analysis', [IncomeAnalysisReportController::class, 'index'])
        ->name('admin.reports.income-analysis');
    Route::get('/reports/product-sales', [ProductSalesReportController::class, 'index'])->name('admin.reports.product-sales');
    Route::get('/reports/inventory', [InventoryReportController::class, 'index'])->name('admin.reports.inventory');
    Route::redirect('reports/bundle-sales', '/admin/reports/harvest-report');
    Route::get('/reports/harvest-report', [HarvestReportController::class, 'index'])
        ->name('admin.reports.harvest');
    Route::get('/reports/unused-balances', UnusedBalancesReportController::class)
        ->name('admin.reports.unused-balances');
    Route::get('/reports/subscription-inventory', SubscriptionInventoryReportController::class)
        ->name('admin.reports.subscription-inventory');
    Route::get('/reports/restock-log', RestockLogReportController::class)
        ->name('admin.reports.restock-log');
    Route::get('/reports/account-credit-applied', AccountCreditAppliedReportController::class)
        ->name('admin.reports.account-credit-applied');
    Route::get('/reports/first-time-renewal', FirstTimeRenewalReportController::class)
        ->name('admin.reports.first-time-renewal');

    // Settings
    Route::get('/settings', [SettingController::class, 'index'])->name('admin.settings');
    Route::put('/settings/customer', [CustomerSettingController::class, 'update'])->name('admin.settings.customer.update');

    Route::get('/apps', [AppController::class, 'index'])->name('admin.apps.index');
    Route::get('/apps/{app}', [AppController::class, 'show'])->name('admin.apps.show');
    Route::post('/apps/{app}', [AppController::class, 'store'])->name('admin.apps.store');
    Route::delete('/apps/{app}', [AppController::class, 'destroy'])->name('admin.apps.destroy');
    Route::get('/apps/{app}/edit', [AppController::class, 'edit'])->name('admin.apps.edit');


    Route::get('/settings/{setting}', [SettingController::class, 'edit'])->name('admin.settings.edit');
    Route::put('/settings', [SettingController::class, 'update'])->name('admin.settings.update');

    // Schedules
    Route::resource('schedules', ScheduleController::class, ['as' => 'admin']);
    Route::put('/schedules/{schedule}/activate', [ScheduleController::class, 'activate'])->name('admin.schedules.activate.update');

    // Schedule Dates
    Route::delete('/schedules/{schedule}/dates/{date?}', [ScheduleDateController::class, 'destroy']);
    Route::resource('schedules.dates', ScheduleDateController::class, ['as' => 'admin']);

    // Schedule Reminder
    Route::post('/schedules/{schedule}/reminder', [ScheduleReminderController::class, 'store'])->name('admin.schedules.reminders.store');

    // Team
    Route::resource('team', TeamController::class)->only(['index', 'update']);

    // Templates
    Route::resource('templates', TemplateController::class, ['as' => 'admin']);

    // Template Previews
    Route::get('/templates/{template}', [TemplatePreviewController::class, 'show'])->name('admin.templates.show');
    Route::post('/templates/{template}/preview', [TemplatePreviewController::class, 'store'])->name('admin.templates.preview.store');

    // Template Segments
    Route::resource('templates.segments', TemplateSegmentController::class)->except('create', 'show');

    // Theme
    Route::get('/theme', [ThemeController::class, 'edit']);
    Route::get('/themes', [ThemeController::class, 'edit']);
    Route::put('/themes/{themes}', [ThemeController::class, 'update']);
    Route::get('/themes/{theme}/fonts', [ThemeController::class, 'getAvailableFonts']);

    // Theme Settings
    Route::put('/settings/theme', [ThemeSettingsController::class, 'update']);

    // Users
    Route::post('/users/{user}/reset-password', [UserController::class, 'resetPassword'])->name('admin.users.resetPassword');
    Route::get('/users/{users}/login-as-user', [UserController::class, 'loginAsUser']);
    Route::resource('users', UserController::class, ['as' => 'admin']);

    // User Cards
    Route::post('/users/{user}/cards', [UserCardController::class, 'store'])->name('admin.users.cards.store');
    Route::delete('/users/{user}/cards/{card}', [UserCardController::class, 'destroy'])->name('admin.users.cards.destroy');

    // User Tags
    Route::get('/users/{users}/tags', [UserTagController::class, 'index']);
    Route::post('/users/{users}/tags', [UserTagController::class, 'store']);
    Route::put('/users/{users}/tags/{tags}', [UserTagController::class, 'update']);
    Route::delete('/users/{users}/tags/{tags}', [UserTagController::class, 'destroy']);

    Route::get('/subscriptions', [SubscriptionController::class, 'index'])->name('admin.subscriptions.index');
    Route::get('/subscriptions/{subscription}/edit', [SubscriptionController::class, 'edit'])->name('admin.subscriptions.edit')->withTrashed();
    Route::put('/subscriptions/{subscription}', [SubscriptionController::class, 'update'])->name('admin.subscriptions.update');

    // Categories
    Route::get('/categories', [CategoryController::class, 'index'])->name('admin.categories.index');
    Route::get('/categories/{category}/edit', [CategoryController::class, 'edit'])->name('admin.categories.edit');

    // PackingGroups
    Route::get('/packing-groups', [PackingGroupController::class, 'index'])->name('admin.packing-groups.index');

    // User Photo
    Route::put('/users/{users}/photo', [UserPhotoController::class, 'update']);

    // Vendors
    Route::resource('vendors', VendorController::class, ['as' => 'admin']);
    Route::put('/vendors/{vendor}/photo', [VendorPhotoController::class, 'update']);
});

// internal admin api
Route::prefix('/admin/api')->middleware('auth.admin')->group(function () {
    // Sluggable
    Route::post('/sluggable/create-slug', [SluggableCreateSlugController::class, 'index']);
});
